# Setup
@host = http://localhost:8082

###
# @name LIST DEVICES (subscribed to a service by api key)
GET {{host}}/v1/devices
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name LIST DEVICES as demo.appio.so
GET {{host}}/demo-appio-so/devices
Authorization: Bearer dev_11111111111111111111111111111111111111111111111111
X-Service-Id: demo_svc_00000000000000000000000000

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name CREATE & LINK DEVICE as ios
POST {{host}}/mobile/devices
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222
X-Service-Id: svc_00000000000000000000000000

{
  "customer_user_id": "cli-test",
  "name": "iPhone 16E",
  "platform": "ios",
  "os_version": "18.3.1",
  "model": "iPhone",
  "device_identifier": "iPhone11,8",
  "notifications_enabled": true
}

> {%
    client.global.set("ios_device_id", response.body.id);
    client.test("Response status should be 201", () => {
        client.assert(response.status === 201, `Response status was: ${response.status}`)
    })
%}

###
# @name CREATE & LINK DEVICE as demo.appio.so
POST {{host}}/demo-appio-so/devices
Authorization: Bearer dev_11111111111111111111111111111111111111111111111111
X-Service-Id: demo_svc_00000000000000000000000000

{
  "customer_user_id": "demo",
  "name": "demo",
  "platform": "ios",
  "os_version": "demo",
  "model": "demo",
  "device_identifier": "demo",
  "notifications_enabled": true
}

> {%
    client.global.set("demo_device_id", response.body.id);
    client.test("Response status should be 201", () => {
        client.assert(response.status === 201, `Response status was: ${response.status}`)
    })
%}

###
# @name GET DEVICE (subscribed to a service by api key)
GET {{host}}/v1/devices/dvc_00000000000000000000000000
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name GET DEVICE as ios
GET {{host}}/v1/devices/{{ios_device_id}}
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name GET DEVICE - not found (subscribed to a service by api key)
GET {{host}}/v1/devices/dvc_00000000xxxxxxxxxxxxxxxxxx
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

> {%
    client.test("Response status should be 404", () => {
        client.assert(response.status === 404, `Response status was: ${response.status}`)
    })
%}

###
# @name LINK DEVICE WITH SERVICE as ios
POST {{host}}/mobile/devices/{{ios_device_id}}/services
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222
X-Service-Id: svc_11111111111111111111111111

{
  "customer_user_id": "demo-cli-test"
}

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name OLD: UPDATE DEVICE as ios  TODO: delete this once ios v1.2 is released
PATCH {{host}}/mobile/devices/{{ios_device_id}}
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222
X-Service-Id: svc_00000000000000000000000000

{
  "data":{
    "notifications_enabled": true,
    "device_token": "80d00eefc688e8dd1071aa3aee592c04aa9de60b88a0e88eb9bf9c527d8609154a058d77dac9fb32fe3382faa00d57bba0b97f61b3687025392db90b9c3c50d2e7b2b282e10cbcb31a9c5d357afbcdcc"
  }
}

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name UPDATE DEVICE as ios
PATCH {{host}}/mobile/devices/{{ios_device_id}}
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222

{
  "notifications_enabled": true,
  "device_token": "80d00eefc688e8dd1071aa3aee592c04aa9de60b88a0e88eb9bf9c527d8609154a058d77dac9fb32fe3382faa00d57bba0b97f61b3687025392db90b9c3c50d2e7b2b282e10cbcb31a9c5d357afbcdcc"
}

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name OLD: UPDATE DEVICE as ios - not found  TODO: delete this once ios v1.2 is released
PATCH {{host}}/mobile/devices/dvc_00000000xxxxxxxxxxxxxxxxxx
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222
X-Service-Id: svc_00000000000000000000000000

{
  "data":{
    "notifications_enabled": false,
    "device_token": "1111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111"
  }
}

> {%
    client.test("Response status should be 404", () => {
        client.assert(response.status === 404, `Response status was: ${response.status}`)
    })
%}

###
# @name UPDATE DEVICE as ios - not found
PATCH {{host}}/mobile/devices/dvc_00000000xxxxxxxxxxxxxxxxxx
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222

{
  "notifications_enabled": false,
  "device_token": "1111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111"
}

> {%
    client.test("Response status should be 404", () => {
        client.assert(response.status === 404, `Response status was: ${response.status}`)
    })
%}

###
# @name DELETE DEVICE (disconnect from service, subscribed to a service by api key)
DELETE {{host}}/v1/devices/{{ios_device_id}}
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name DELETE DEVICE - re-try fails (disconnect from service, subscribed to a service by api key)
DELETE {{host}}/v1/devices/{{ios_device_id}}
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

> {%
    client.test("Response status should be 404", () => {
        client.assert(response.status === 404, `Response status was: ${response.status}`)
    })
%}

###
# @name OLD: UPDATE DEVICE as ios - still works after delete/disconnect  TODO: delete this once ios v1.2 is released
PATCH {{host}}/mobile/devices/{{ios_device_id}}
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222
X-Service-Id: svc_00000000000000000000000000

{
  "data":{
    "notifications_enabled": true,
    "device_token": "1111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111"
  }
}

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name UPDATE DEVICE as ios - still works after delete/disconnect
PATCH {{host}}/mobile/devices/{{ios_device_id}}
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222

{
  "notifications_enabled": true,
  "device_token": "1111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111"
}

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name (create device for delete test below)
POST {{host}}/demo-appio-so/devices
Authorization: Bearer dev_11111111111111111111111111111111111111111111111111
X-Service-Id: demo_svc_00000000000000000000000000

{
  "customer_user_id": "demo2",
  "name": "demo2",
  "platform": "ios",
  "os_version": "demo2",
  "model": "demo2",
  "device_identifier": "demo2",
  "notifications_enabled": false
}

> {%
    client.global.set("demo_device_id_2", response.body.id);
    client.test("Response status should be 201", () => {
        client.assert(response.status === 201, `Response status was: ${response.status}`)
    })
%}

###
# @name DELETE DEVICE as ios (disconnect from service)
DELETE {{host}}/mobile/devices/{{demo_device_id_2}}
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222
X-Service-Id: demo_svc_00000000000000000000000000

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}



###
# @name (create device for delete test below)
POST {{host}}/mobile/devices
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222
X-Service-Id: svc_00000000000000000000000000

{
  "customer_user_id": "demo3",
  "name": "demo3",
  "platform": "ios",
  "os_version": "demo3",
  "model": "demo3",
  "device_identifier": "demo3",
  "notifications_enabled": false
}

> {%
    client.test("Response status should be 201", () => {
        client.assert(response.status === 201, `Response status was: ${response.status}`)
    })
%}

###
# @name DELETE DEVICES for user (disconnect from service)
DELETE {{host}}/v1/devices?user_id=demo3
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}