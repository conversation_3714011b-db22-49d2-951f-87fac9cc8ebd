package services

import (
	"api.appio.so/models"
	"api.appio.so/internal/mocks"
	"api.appio.so/pkg"
	"context"
	"errors"
	"github.com/jackc/pgx/v5/pgconn"
	"testing"

	"github.com/appio-so/go-appioid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"go.uber.org/zap"
)

func dvcCreateMocks(t *testing.T) (*DeviceService, *mocks.MockDeviceRepositoryInterface, *mocks.MockDeviceServiceRepositoryInterface, *gomock.Controller) {
	ctrl := gomock.NewController(t)
	mockDeviceRepo := mocks.NewMockDeviceRepositoryInterface(ctrl)
	mockDeviceServiceRepo := mocks.NewMockDeviceServiceRepositoryInterface(ctrl)
	mockDeviceNameRepo := mocks.NewMockDeviceNameRepositoryInterface(ctrl)
	logger := zap.NewNop()
	db := &pgxpool.Pool{}
	deviceService := NewDeviceService(mockDeviceRepo, mockDeviceServiceRepo, mockDeviceNameRepo, db, logger)

	deviceService.TxExecutor = func(ctx context.Context, pool *pgxpool.Pool, logger *zap.Logger, fn func(tx pgx.Tx) error) error {
		return fn(nil)
	}

	return deviceService, mockDeviceRepo, mockDeviceServiceRepo, ctrl
}

func TestFindByID(t *testing.T) {
	svcID, _ := appioid.New("svc_00000000000000000000000000")
	dvcID, _ := appioid.New("dvc_00000000000000000000000000")

	t.Run("Device found", func(t *testing.T) {
		deviceService, mockDeviceRepo, _, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		mockDeviceRepo.EXPECT().
			FindByID(gomock.Any(), svcID, dvcID).
			Return(&models.DeviceRecord{ID: dvcID}, nil)

		device, err := deviceService.FindByID(context.Background(), svcID, dvcID)

		assert.NoError(t, err)
		assert.NotNil(t, device)
		assert.Equal(t, dvcID, device.ID)
	})

	t.Run("Device not found", func(t *testing.T) {
		deviceService, mockDeviceRepo, _, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		mockDeviceRepo.EXPECT().
			FindByID(gomock.Any(), svcID, dvcID).
			Return((*models.DeviceRecord)(nil), pgx.ErrNoRows)

		device, err := deviceService.FindByID(context.Background(), svcID, dvcID)

		assert.Error(t, err)
		assert.Nil(t, device)
		assert.Equal(t, pkg.ErrNotFound, err)
	})

	t.Run("Internal error", func(t *testing.T) {
		deviceService, mockDeviceRepo, _, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		mockDeviceRepo.EXPECT().
			FindByID(gomock.Any(), svcID, dvcID).
			Return(nil, errors.New("internal error"))

		device, err := deviceService.FindByID(context.Background(), svcID, dvcID)

		assert.Error(t, err)
		assert.Nil(t, device)
		assert.ErrorIs(t, err, pkg.ErrInternal)
	})
}

func TestList(t *testing.T) {
	svcID, _ := appioid.New("svc_00000000000000000000000000")

	t.Run("List success", func(t *testing.T) {
		deviceService, mockDeviceRepo, _, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		// Prepare two sample devices.
		dev1, _ := appioid.New("dvc_00000000000000000000000001")
		dev2, _ := appioid.New("dvc_00000000000000000000000002")
		expectedRecords := []models.DeviceRecord{
			{ID: dev1},
			{ID: dev2},
		}
		expectedDevices := []models.DeviceResponse{
			{DeviceRecord: models.DeviceRecord{ID: dev1}},
			{DeviceRecord: models.DeviceRecord{ID: dev2}},
		}

		mockDeviceRepo.EXPECT().
			List(gomock.Any(), svcID).
			Return(expectedRecords, nil)

		devices, err := deviceService.List(context.Background(), svcID)
		assert.NoError(t, err)
		assert.Equal(t, expectedDevices, devices)
	})

	t.Run("List error", func(t *testing.T) {
		deviceService, mockDeviceRepo, _, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		dbErr := errors.New("db error")
		mockDeviceRepo.EXPECT().
			List(gomock.Any(), svcID).
			Return(nil, dbErr)

		devices, err := deviceService.List(context.Background(), svcID)
		assert.Error(t, err)
		assert.Nil(t, devices)
		// service wraps repository errors with pkg.ErrInternal
		assert.Equal(t, pkg.ErrInternal, err)
	})
}

func TestFindByCustomerUserID(t *testing.T) {
	svcID, _ := appioid.New("svc_00000000000000000000000000")
	customerUserID := "testUser123"

	t.Run("Devices found", func(t *testing.T) {
		deviceService, mockDeviceRepo, _, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		dev1, _ := appioid.New("dvc_00000000000000000000000001")
		dev2, _ := appioid.New("dvc_00000000000000000000000002")
		expectedRecords := []models.DeviceRecord{
			{ID: dev1, CustomerUserID: customerUserID},
			{ID: dev2, CustomerUserID: customerUserID},
		}
		expectedDevices := []models.DeviceResponse{
			{DeviceRecord: models.DeviceRecord{ID: dev1, CustomerUserID: customerUserID}},
			{DeviceRecord: models.DeviceRecord{ID: dev2, CustomerUserID: customerUserID}},
		}

		mockDeviceRepo.EXPECT().
			FindByCustomerUserID(gomock.Any(), svcID, customerUserID).
			Return(expectedRecords, nil)

		devices, err := deviceService.FindByCustomerUserID(context.Background(), svcID, customerUserID)
		assert.NoError(t, err)
		assert.Equal(t, expectedDevices, devices)
	})

	t.Run("No devices found", func(t *testing.T) {
		deviceService, mockDeviceRepo, _, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		mockDeviceRepo.EXPECT().
			FindByCustomerUserID(gomock.Any(), svcID, customerUserID).
			Return(nil, pgx.ErrNoRows)

		devices, err := deviceService.FindByCustomerUserID(context.Background(), svcID, customerUserID)
		assert.Error(t, err)
		assert.Nil(t, devices)
		assert.Equal(t, pkg.ErrNotFound, err)
	})

	t.Run("Internal error", func(t *testing.T) {
		deviceService, mockDeviceRepo, _, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		mockDeviceRepo.EXPECT().
			FindByCustomerUserID(gomock.Any(), svcID, customerUserID).
			Return(nil, errors.New("internal error"))

		devices, err := deviceService.FindByCustomerUserID(context.Background(), svcID, customerUserID)
		assert.Error(t, err)
		assert.Nil(t, devices)
		assert.ErrorIs(t, err, pkg.ErrInternal)
	})
}

func TestCreateAndLink(t *testing.T) {
	svcID, _ := appioid.New("svc_00000000000000000000000000")
	dvcReq := models.DeviceCreateRequest{
		CustomerUserID: "testUser",
		DeviceData: models.DeviceData{
			Name:      "Test Device",
			Platform:  "iOS",
			OsVersion: "14.0",
			Model:     "iPhone 12",
		},
	}

	t.Run("CreateAndLink success", func(t *testing.T) {
		deviceService, mockDeviceRepo, mockDeviceServiceRepo, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		// Expect the device creation call.
		mockDeviceRepo.EXPECT().
			CreateTx(gomock.Any(), gomock.Any(), gomock.Any(), dvcReq).
			Return(nil)
		// Expect the device-service linking call.
		mockDeviceServiceRepo.EXPECT().
			CreateTx(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), svcID, dvcReq.CustomerUserID).
			Return(nil)

		dvcID, err := deviceService.CreateAndLink(context.Background(), svcID, dvcReq)
		assert.NoError(t, err)
		assert.NotNil(t, dvcID)
	})

	t.Run("CreateAndLink fails on device creation", func(t *testing.T) {
		deviceService, mockDeviceRepo, _, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		createErr := errors.New("create device error")
		mockDeviceRepo.EXPECT().
			CreateTx(gomock.Any(), gomock.Any(), gomock.Any(), dvcReq).
			Return(createErr)

		dvcID, err := deviceService.CreateAndLink(context.Background(), svcID, dvcReq)
		assert.Error(t, err)
		assert.Nil(t, dvcID)
		assert.Equal(t, pkg.ErrInternal, err)
	})

	t.Run("CreateAndLink fails on device-service linking", func(t *testing.T) {
		deviceService, mockDeviceRepo, mockDeviceServiceRepo, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		linkErr := errors.New("create device-service error")
		mockDeviceRepo.EXPECT().
			CreateTx(gomock.Any(), gomock.Any(), gomock.Any(), dvcReq).
			Return(nil)
		mockDeviceServiceRepo.EXPECT().
			CreateTx(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), svcID, dvcReq.CustomerUserID).
			Return(linkErr)

		dvcID, err := deviceService.CreateAndLink(context.Background(), svcID, dvcReq)
		assert.Error(t, err)
		assert.Nil(t, dvcID)
		assert.Equal(t, pkg.ErrInternal, err)
	})

	t.Run("CreateAndLink duplicate constraint error", func(t *testing.T) {
		deviceService, mockDeviceRepo, mockDeviceServiceRepo, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		mockDeviceRepo.EXPECT().
			CreateTx(gomock.Any(), gomock.Any(), gomock.Any(), dvcReq).
			Return(nil)

		// Simulate duplicate constraint error
		pgErr := &pgconn.PgError{
			Code:           "23505",
			ConstraintName: "some_constraint",
			Message:        "duplicate key",
		}
		mockDeviceServiceRepo.EXPECT().
			CreateTx(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), svcID, dvcReq.CustomerUserID).
			Return(pgErr)

		dvcID, err := deviceService.CreateAndLink(context.Background(), svcID, dvcReq)
		assert.Error(t, err)
		assert.Nil(t, dvcID)
		assert.Equal(t, pkg.ErrInternal, err)
	})
}

func TestLinkWithService(t *testing.T) {
	svcID, _ := appioid.New("svc_00000000000000000000000000")
	dvcID, _ := appioid.New("dvc_00000000000000000000000000")
	dvcLinkReq := models.DeviceLinkServiceRequest{
		CustomerUserID: "testUser",
	}

	t.Run("LinkWithService success", func(t *testing.T) {
		deviceService, _, mockDeviceServiceRepo, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		mockDeviceServiceRepo.EXPECT().
			Create(gomock.Any(), gomock.Any(), dvcID, svcID, dvcLinkReq.CustomerUserID).
			Return(nil)

		err := deviceService.LinkWithService(context.Background(), svcID, dvcID, dvcLinkReq)
		assert.NoError(t, err)
	})

	t.Run("LinkWithService duplicate error returns ErrMatch", func(t *testing.T) {
		deviceService, _, mockDeviceServiceRepo, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		pgErr := &pgconn.PgError{
			Code:           "23505",
			ConstraintName: "uniq_active_dvc_svc",
			Message:        "duplicate key",
		}
		mockDeviceServiceRepo.EXPECT().
			Create(gomock.Any(), gomock.Any(), dvcID, svcID, dvcLinkReq.CustomerUserID).
			Return(pgErr)

		err := deviceService.LinkWithService(context.Background(), svcID, dvcID, dvcLinkReq)
		assert.Equal(t, pkg.ErrMatch, err)
	})

	t.Run("LinkWithService generic error returns ErrInternal", func(t *testing.T) {
		deviceService, _, mockDeviceServiceRepo, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		genErr := errors.New("some error")
		mockDeviceServiceRepo.EXPECT().
			Create(gomock.Any(), gomock.Any(), dvcID, svcID, dvcLinkReq.CustomerUserID).
			Return(genErr)

		err := deviceService.LinkWithService(context.Background(), svcID, dvcID, dvcLinkReq)
		assert.Equal(t, pkg.ErrInternal, err)
	})

	t.Run("LinkWithService different constraint error returns ErrInternal", func(t *testing.T) {
		deviceService, _, mockDeviceServiceRepo, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		pgErr := &pgconn.PgError{
			Code:           "23505",
			ConstraintName: "different_constraint", // Different constraint name
			Message:        "duplicate key",
		}
		mockDeviceServiceRepo.EXPECT().
			Create(gomock.Any(), gomock.Any(), dvcID, svcID, dvcLinkReq.CustomerUserID).
			Return(pgErr)

		err := deviceService.LinkWithService(context.Background(), svcID, dvcID, dvcLinkReq)
		assert.Equal(t, pkg.ErrInternal, err) // Should be ErrInternal, not ErrMatch
	})

	t.Run("LinkWithService different postgres error code returns ErrInternal", func(t *testing.T) {
		deviceService, _, mockDeviceServiceRepo, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		pgErr := &pgconn.PgError{
			Code:           "23503", // Different error code
			ConstraintName: "uniq_active_dvc_svc",
			Message:        "foreign key violation",
		}
		mockDeviceServiceRepo.EXPECT().
			Create(gomock.Any(), gomock.Any(), dvcID, svcID, dvcLinkReq.CustomerUserID).
			Return(pgErr)

		err := deviceService.LinkWithService(context.Background(), svcID, dvcID, dvcLinkReq)
		assert.Equal(t, pkg.ErrInternal, err) // Should be ErrInternal, not ErrMatch
	})
}

func TestUpdate(t *testing.T) {
	dvcID := appioid.MustParse("dvc_00000000000000000000000000")
	updateReq := models.DeviceUpdateRequest{
		Name:      "Test Device",
		OsVersion: "14.0",
		Model:     "iPhone 12",
	}

	t.Run("Update success", func(t *testing.T) {
		deviceService, mockDeviceRepo, _, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		mockDeviceRepo.EXPECT().
			Update(gomock.Any(), dvcID, updateReq).
			Return(true, nil)

		err := deviceService.Update(context.Background(), dvcID, updateReq)
		assert.NoError(t, err)
	})

	t.Run("Update error returns ErrInternal", func(t *testing.T) {
		deviceService, mockDeviceRepo, _, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		updateErr := errors.New("update error")
		mockDeviceRepo.EXPECT().
			Update(gomock.Any(), dvcID, updateReq).
			Return(false, updateErr)

		// When the first repository call fails, the transaction is rolled back
		// and the second repository call is never made

		err := deviceService.Update(context.Background(), dvcID, updateReq)
		assert.Error(t, err)
		assert.Equal(t, pkg.ErrInternal, err)
	})
}

func TestDelete(t *testing.T) {
	svcID, _ := appioid.New("svc_00000000000000000000000000")
	dvcID, _ := appioid.New("dvc_00000000000000000000000000")

	t.Run("Delete success", func(t *testing.T) {
		deviceService, _, mockDeviceServiceRepo, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		mockDeviceServiceRepo.EXPECT().
			Deactivate(gomock.Any(), svcID, dvcID).
			Return(true, nil)

		err := deviceService.Deactivate(context.Background(), svcID, dvcID)
		assert.NoError(t, err)
	})

	t.Run("Delete error returns ErrInternal", func(t *testing.T) {
		deviceService, _, mockDeviceServiceRepo, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		delErr := errors.New("delete error")
		mockDeviceServiceRepo.EXPECT().
			Deactivate(gomock.Any(), svcID, dvcID).
			Return(false, delErr)

		err := deviceService.Deactivate(context.Background(), svcID, dvcID)
		assert.Error(t, err)
		assert.Equal(t, pkg.ErrInternal, err)
	})

	t.Run("Delete not found returns ErrNotFound", func(t *testing.T) {
		deviceService, _, mockDeviceServiceRepo, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		mockDeviceServiceRepo.EXPECT().
			Deactivate(gomock.Any(), svcID, dvcID).
			Return(false, nil) // No error but not found

		err := deviceService.Deactivate(context.Background(), svcID, dvcID)
		assert.Error(t, err)
		assert.Equal(t, pkg.ErrNotFound, err)
	})
}

func TestDeactivateByCustomerUserID(t *testing.T) {
	svcID, _ := appioid.New("svc_00000000000000000000000000")
	customerUserID := "testUser123"

	t.Run("Deactivate success", func(t *testing.T) {
		deviceService, _, mockDeviceServiceRepo, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		dev1 := appioid.MustParse("dvc_00000000000000000000000001")
		dev2 := appioid.MustParse("dvc_00000000000000000000000002")
		expectedDeviceIDs := []appioid.ID{*dev1, *dev2}

		mockDeviceServiceRepo.EXPECT().
			DeactivateByCustomerUserID(gomock.Any(), svcID, customerUserID).
			Return(expectedDeviceIDs, nil)

		deviceIDs, err := deviceService.DeactivateByCustomerUserID(context.Background(), svcID, customerUserID)
		assert.NoError(t, err)
		assert.Equal(t, expectedDeviceIDs, deviceIDs)
	})

	t.Run("Deactivate error returns ErrInternal", func(t *testing.T) {
		deviceService, _, mockDeviceServiceRepo, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		deactivateErr := errors.New("deactivate error")
		mockDeviceServiceRepo.EXPECT().
			DeactivateByCustomerUserID(gomock.Any(), svcID, customerUserID).
			Return(nil, deactivateErr)

		deviceIDs, err := deviceService.DeactivateByCustomerUserID(context.Background(), svcID, customerUserID)
		assert.Error(t, err)
		assert.Nil(t, deviceIDs)
		assert.Equal(t, pkg.ErrInternal, err)
	})

	t.Run("Deactivate no devices found", func(t *testing.T) {
		deviceService, _, mockDeviceServiceRepo, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		mockDeviceServiceRepo.EXPECT().
			DeactivateByCustomerUserID(gomock.Any(), svcID, customerUserID).
			Return([]appioid.ID{}, nil) // Empty slice, no devices found

		deviceIDs, err := deviceService.DeactivateByCustomerUserID(context.Background(), svcID, customerUserID)
		assert.NoError(t, err)
		assert.Empty(t, deviceIDs)
	})
}

func TestDeviceResponse_MarketingName(t *testing.T) {
	svcID, _ := appioid.New("svc_00000000000000000000000000")

	t.Run("iOS device marketing name", func(t *testing.T) {
		deviceService, mockDeviceRepo, _, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		dvcID, _ := appioid.New("dvc_00000000000000000000000000")
		deviceRecord := &models.DeviceRecord{
			ID: dvcID,
			DeviceData: models.DeviceData{
				Platform:         "ios",
				DeviceIdentifier: "iPhone14,2", // iPhone 13 Pro
				MarketingName:    "iPhone 13 Pro", // Now stored in database
			},
		}

		mockDeviceRepo.EXPECT().
			FindByID(gomock.Any(), svcID, dvcID).
			Return(deviceRecord, nil)

		device, err := deviceService.FindByID(context.Background(), svcID, dvcID)
		assert.NoError(t, err)
		assert.NotNil(t, device)
		assert.Equal(t, "iPhone 13 Pro", device.MarketingName) // Should have marketing name from database
	})

	t.Run("Android device marketing name", func(t *testing.T) {
		deviceService, mockDeviceRepo, _, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		dvcID, _ := appioid.New("dvc_00000000000000000000000000")
		deviceRecord := &models.DeviceRecord{
			ID: dvcID,
			DeviceData: models.DeviceData{
				Platform:         "android",
				DeviceIdentifier: "SM-G998B", // Samsung Galaxy S21 Ultra
				MarketingName:    "Samsung Galaxy S21 Ultra", // Now stored in database
			},
		}

		mockDeviceRepo.EXPECT().
			FindByID(gomock.Any(), svcID, dvcID).
			Return(deviceRecord, nil)

		device, err := deviceService.FindByID(context.Background(), svcID, dvcID)
		assert.NoError(t, err)
		assert.NotNil(t, device)
		assert.Equal(t, "Samsung Galaxy S21 Ultra", device.MarketingName) // Should have marketing name from database
	})

	t.Run("Unknown platform device", func(t *testing.T) {
		deviceService, mockDeviceRepo, _, ctrl := dvcCreateMocks(t)
		defer ctrl.Finish()

		dvcID, _ := appioid.New("dvc_00000000000000000000000000")
		deviceRecord := &models.DeviceRecord{
			ID: dvcID,
			DeviceData: models.DeviceData{
				Platform:         "windows",
				DeviceIdentifier: "Surface-Pro-8",
				MarketingName:    "", // Empty marketing name for unknown platforms
			},
		}

		mockDeviceRepo.EXPECT().
			FindByID(gomock.Any(), svcID, dvcID).
			Return(deviceRecord, nil)

		device, err := deviceService.FindByID(context.Background(), svcID, dvcID)
		assert.NoError(t, err)
		assert.NotNil(t, device)
		assert.Empty(t, device.MarketingName) // Should be empty for unknown platforms
	})
}
