package config

import (
	"fmt"
	"strings"
	"time"

	"github.com/appio-so/go-zaplog"
	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

type Config struct {
	FileName    string
	DB          DBConfig
	Auth        AuthConfig
	Server      ServerConfig
	Fingerprint struct {
		Log bool `mapstructure:"log"`
	}
	Sentry     SentryConfig
	PropelAuth PropelAuthConfig
}

type DBConfig struct {
	Source     string `mapstructure:"source"`
	SourceFing string `mapstructure:"source_fing"`
	LogQueries bool   `mapstructure:"log_queries"`
}

type AuthConfig struct {
	App     string `mapstructure:"app"`
	Demo    string `mapstructure:"demo"`
	IOS     string `mapstructure:"ios"`
	Android string `mapstructure:"android"`
}

type ServerConfig struct {
	Name     string `mapstructure:"name"`
	Env      string `mapstructure:"env"`
	Port     int    `mapstructure:"port"`
	LogLevel string `mapstructure:"log_level"` // config watch update
	Timeout  string `mapstructure:"timeout"`
}

type SentryConfig struct {
	DSN             string  `mapstructure:"dsn"`
	SendDefaultPII  bool    `mapstructure:"send_default_pii"`
	TraceSampleRate float64 `mapstructure:"trace_sample_rate"`
}

type PropelAuthConfig struct {
	URL string `mapstructure:"url"`
	Key string `mapstructure:"key"`
}

func LoadConfig(fileName string) (*Config, error) {
	viper.SetConfigName(fileName)
	viper.SetConfigType("toml")
	viper.AddConfigPath("./configs")

	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("reading config file: %w", err)
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("unmarshaling config: %w", err)
	}
	config.FileName = fileName

	viper.AutomaticEnv() // Automatically read environment variables. f.e. SERVER_PORT="123"
	viper.WatchConfig()  // listen to config changes

	// Validate the configuration
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("config validation failed: %w", err)
	}

	return &config, nil
}

func WatchConfig(cfg *Config, logger *zap.Logger) {
	viper.OnConfigChange(func(e fsnotify.Event) {
		logger.Info("config file changed", zap.String("name", e.Name))

		newCfg, err := LoadConfig(cfg.FileName)
		if err != nil {
			logger.Error("reloading config", zap.Error(err))
			return
		}

		*cfg = *newCfg // Global config update

		// Add custom services to configure. Change function to WatchConfig(cfg *Config, logger *zap.Logger, services []WatchInterface)

		if err := zaplog.SetLogLevel(cfg.Server.LogLevel); err != nil {
			logger.Error("changing log level", zap.Error(err))
		}
	})
}

// Validate validates the entire configuration
func (c *Config) Validate() error {
	if err := c.Server.Validate(); err != nil {
		return fmt.Errorf("server config: %w", err)
	}

	if err := c.DB.Validate(); err != nil {
		return fmt.Errorf("database config: %w", err)
	}

	if err := c.Auth.Validate(); err != nil {
		return fmt.Errorf("auth config: %w", err)
	}

	return nil
}

// Validate validates server configuration
func (s *ServerConfig) Validate() error {
	if s.Name == "" {
		return fmt.Errorf("server name is required")
	}

	if s.Env == "" {
		return fmt.Errorf("server environment is required")
	}

	validEnvs := []string{"dev", "staging", "prod", "test"}
	if !contains(validEnvs, s.Env) {
		return fmt.Errorf("server environment must be one of: %s, got: %s", strings.Join(validEnvs, ", "), s.Env)
	}

	if s.Port <= 0 || s.Port > 65535 {
		return fmt.Errorf("server port must be between 1 and 65535, got: %d", s.Port)
	}

	if s.LogLevel == "" {
		return fmt.Errorf("server log level is required")
	}

	validLogLevels := []string{"debug", "info", "warn", "error", "fatal"}
	if !contains(validLogLevels, s.LogLevel) {
		return fmt.Errorf("server log level must be one of: %s, got: %s", strings.Join(validLogLevels, ", "), s.LogLevel)
	}

	if s.Timeout == "" {
		return fmt.Errorf("server timeout is required")
	}

	if _, err := time.ParseDuration(s.Timeout); err != nil {
		return fmt.Errorf("server timeout must be a valid duration (e.g., '30s', '5m'), got: %s", s.Timeout)
	}

	return nil
}

// Validate validates database configuration
func (d *DBConfig) Validate() error {
	if d.Source == "" {
		return fmt.Errorf("database source is required")
	}

	if d.SourceFing == "" {
		return fmt.Errorf("fingerprint database source is required")
	}

	return nil
}

// Validate validates auth configuration
func (a *AuthConfig) Validate() error {
	if a.App == "" {
		return fmt.Errorf("auth app key is required")
	}

	if len(a.App) < 50 {
		return fmt.Errorf("auth app key must be at least 50 characters long, got: %d", len(a.App))
	}

	if a.Demo == "" {
		return fmt.Errorf("auth demo key is required")
	}

	if len(a.Demo) < 50 {
		return fmt.Errorf("auth demo key must be at least 50 characters long, got: %d", len(a.Demo))
	}

	if a.IOS == "" {
		return fmt.Errorf("auth iOS key is required")
	}

	if len(a.IOS) < 50 {
		return fmt.Errorf("auth iOS key must be at least 50 characters long, got: %d", len(a.IOS))
	}

	if a.Android == "" {
		return fmt.Errorf("auth Android key is required")
	}

	if len(a.Android) < 50 {
		return fmt.Errorf("auth Android key must be at least 50 characters long, got: %d", len(a.Android))
	}

	// Ensure all keys are different
	keys := []string{a.App, a.Demo, a.IOS, a.Android}
	if !allUnique(keys) {
		return fmt.Errorf("all auth keys must be unique")
	}

	return nil
}

// Helper functions
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func allUnique(vals []string) bool {
	seen := make(map[string]struct{})
	for _, v := range vals {
		if _, ok := seen[v]; ok {
			return false
		}
		seen[v] = struct{}{}
	}
	return true
}
