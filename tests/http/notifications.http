# Setup
@host = http://localhost:8082

###
# @name LIST NOTIFICATIONS
GET {{host}}/v1/notifications
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name LIST NOTIFICATIONS with status filter
GET {{host}}/v1/notifications?status=queued
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name LIST NOTIFICATIONS for device
GET {{host}}/v1/notifications?device_id=dvc_00000000000000000000000000
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name LIST NOTIFICATIONS for device with status filter
GET {{host}}/v1/notifications?device_id=dvc_00000000000000000000000000&status=queued
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name LIST NOTIFICATIONS for user
GET {{host}}/v1/notifications?user_id=ac640c1edfc5e446968dece24f7f458fdbab89c8f533d96cbc545517c51bdf1a
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name LIST NOTIFICATIONS for user with status filter
GET {{host}}/v1/notifications?user_id=ac640c1edfc5e446968dece24f7f458fdbab89c8f533d96cbc545517c51bdf1a&status=queued
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name LIST NOTIFICATIONS as demo
GET {{host}}/demo-appio-so/notifications
Authorization: Bearer dev_11111111111111111111111111111111111111111111111111
X-Service-Id: demo_svc_00000000000000000000000000

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name LIST NOTIFICATIONS as ios
GET {{host}}/mobile/notifications
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222
X-Service-Id: svc_00000000000000000000000000
X-Device-Id: dvc_00000000000000000000000000

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name CREATE NOTIFICATION
POST {{host}}/v1/notifications
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

{
  "payload": {"message":  "testing"}
}

> {%
    client.global.set("notification_id", response.body.id);
    client.test("Response status should be 201", () => {
        client.assert(response.status === 201, `Response status was: ${response.status}`)
    })
%}

###
# @name CREATE NOTIFICATION scheduled in 10 minutes
< {%
    request.variables.set("scheduled_at", (new Date(Date.now() + (10 * 60 * 1000))).toISOString())
%}

POST {{host}}/v1/notifications
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

{
  "payload": {"message":  "testing"},
  "scheduled_at": "{{scheduled_at}}"
}

> {%
    client.test("Response status should be 201", () => {
        client.assert(response.status === 201, `Response status was: ${response.status}`)
    })
%}

###
# @name CREATE NOTIFICATION invalid scheduled in 31 days
< {%
    request.variables.set("scheduled_at", (new Date(Date.now() + (31 * 24 * 60 * 60 * 1000))).toISOString())
%}

POST {{host}}/v1/notifications
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

{
  "payload": {"message":  "testing"},
  "scheduled_at": "{{scheduled_at}}"
}

> {%
    client.test("Response status should be 400", () => {
        client.assert(response.status === 400, `Response status was: ${response.status}`)
    })
%}

###
# @name CREATE NOTIFICATION scheduled with invalid date/time
< {%
    request.variables.set("scheduled_at", "invalid")
%}

POST {{host}}/v1/notifications
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

{
  "payload": {"message":  "testing"},
  "scheduled_at": "{{scheduled_at}}"
}

> {%
    client.test("Response status should be 400", () => {
        client.assert(response.status === 400, `Response status was: ${response.status}`)
    })
%}

###
# @name CREATE NOTIFICATION for device
POST {{host}}/v1/notifications?device_id=dvc_00000000000000000000000000
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

{
  "payload": {"message":  "notification for device"}
}

> {%
    client.global.set("device_notification_id", response.body.id);
    client.test("Response status should be 201", () => {
        client.assert(response.status === 201, `Response status was: ${response.status}`)
    })
%}

###
# @name CREATE NOTIFICATION for user
POST {{host}}/v1/notifications?user_id=ac640c1edfc5e446968dece24f7f458fdbab89c8f533d96cbc545517c51bdf1a
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

{
  "payload": {"message":  "notification for user"}
}

> {%
    client.global.set("user_notification_id", response.body.id);
    client.test("Response status should be 201", () => {
        client.assert(response.status === 201, `Response status was: ${response.status}`)
    })
%}

###
# @name CREATE NOTIFICATION as demo.appio.so
POST {{host}}/demo-appio-so/notifications
Authorization: Bearer dev_11111111111111111111111111111111111111111111111111
X-Service-Id: demo_svc_00000000000000000000000000

{
  "payload": {"message":  "testing"}
}

> {%
    client.global.set("demo_notification_id", response.body.id);
    client.test("Response status should be 201", () => {
        client.assert(response.status === 201, `Response status was: ${response.status}`)
    })
%}

###
# @name GET NOTIFICATION
GET {{host}}/v1/notifications/{{notification_id}}
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name GET NOTIFICATION - not found
GET {{host}}/v1/notifications/ntf_00000000xxxxxxxxxxxxxxxxxx
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

> {%
    client.test("Response status should be 404", () => {
        client.assert(response.status === 404, `Response status was: ${response.status}`)
    })
%}

###
## @name MARK NOTIFICATION DELIVERED as ios - not queued automatically therefore will return 404
#PATCH {{host}}/mobile/notifications/{{notification_id}}/delivered
#Authorization: Bearer dev_22222222222222222222222222222222222222222222222222
#X-Service-Id: svc_00000000000000000000000000
#X-Device-Id: dvc_00000000000000000000000000
#
#> {%
#    client.test("Response status should be 200", () => {
#        client.assert(response.status === 200, `Response status was: ${response.status}`)
#    })
#%}

###
# @name MARK NOTIFICATION DELIVERED as ios - not found
PATCH {{host}}/mobile/notifications/ntf_00000000xxxxxxxxxxxxxxxxxx/delivered
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222
X-Service-Id: svc_00000000000000000000000000
X-Device-Id: dvc_00000000000000000000000000

> {%
    client.test("Response status should be 404", () => {
        client.assert(response.status === 404, `Response status was: ${response.status}`)
    })
%}

###
# @name DELETE NOTIFICATION
# n/a

###
