package repositories

import (
	"context"
	"fmt"
	"strings"

	"api.appio.so/models"
	"github.com/appio-so/go-appioid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type DeviceRepositoryInterface interface {
	FindByID(ctx context.Context, svcID, dvcID *appioid.ID) (*models.DeviceRecord, error)
	FindByCustomerUserID(ctx context.Context, svcID *appioid.ID, customerUserID string) ([]models.DeviceRecord, error)
	List(ctx context.Context, svcID *appioid.ID) ([]models.DeviceRecord, error)
	CreateTx(tx pgx.Tx, ctx context.Context, dvcID *appioid.ID, dvcReq models.DeviceCreateRequest) error
	Update(ctx context.Context, dvcID *appioid.ID, dvcUpdateReq models.DeviceUpdateRequest) (bool, error)
	UpdateTx(tx pgx.Tx, ctx context.Context, dvcID *appioid.ID, dvcUpdateReq models.DeviceUpdateRequest) (bool, error)
	UpdateLastSeenAt(ctx context.Context, dvcID *appioid.ID) error
}

type DeviceRepository struct {
	DB *pgxpool.Pool
}

func NewDeviceRepository(db *pgxpool.Pool) *DeviceRepository {
	return &DeviceRepository{
		DB: db,
	}
}

// Returns only active, without `customer_user_id`
func (r *DeviceRepository) FindByID(ctx context.Context, svcID, dvcID *appioid.ID) (*models.DeviceRecord, error) {
	var dvcRsp models.DeviceRecord
	query := `SELECT d.id, ds.customer_user_id, d.name, d.platform, d.os_version, d.model, d.device_token, d.device_identifier, d.notifications_enabled, d.marketing_name
		FROM devices d
		JOIN dvc_svc ds ON ds.device_id=d.id
		JOIN services s ON s.id=ds.service_id
		WHERE d.id=@device_id AND ds.service_id=@service_id AND ds.deactivated_at IS NULL
		ORDER BY d.created_at DESC`
	args := pgx.NamedArgs{
		"device_id":  dvcID,
		"service_id": svcID,
	}
	err := r.DB.QueryRow(ctx, query, args).Scan(
		&dvcRsp.ID,
		&dvcRsp.CustomerUserID,
		&dvcRsp.Name,
		&dvcRsp.Platform,
		&dvcRsp.OsVersion,
		&dvcRsp.Model,
		&dvcRsp.DeviceToken,
		&dvcRsp.DeviceIdentifier,
		&dvcRsp.NotificationsEnabled,
		&dvcRsp.MarketingName,
	)

	if err != nil {
		return nil, fmt.Errorf("finding device by ID: %w", err)
	}
	return &dvcRsp, nil
}

func (r *DeviceRepository) FindByCustomerUserID(ctx context.Context, svcID *appioid.ID, customerUserID string) ([]models.DeviceRecord, error) {
	query := `SELECT d.id, ds.customer_user_id, d.name, d.platform, d.os_version, d.model, d.device_token, d.device_identifier, d.notifications_enabled, d.marketing_name
		FROM devices d
		JOIN dvc_svc ds ON ds.device_id=d.id
		JOIN services s ON s.id=ds.service_id
		WHERE ds.customer_user_id=@customer_user_id AND ds.service_id=@service_id AND ds.deactivated_at IS NULL
		ORDER BY d.created_at DESC`
	args := pgx.NamedArgs{
		"customer_user_id": customerUserID,
		"service_id":       svcID,
	}
	return QueryList[models.DeviceRecord](ctx, r.DB, query, args)
}

func (r *DeviceRepository) List(ctx context.Context, svcID *appioid.ID) ([]models.DeviceRecord, error) {
	query := `
        SELECT d.id, ds.customer_user_id, d.name, d.platform, d.os_version, d.model, d.device_token, d.device_identifier, d.notifications_enabled, d.marketing_name
        FROM devices d
        JOIN dvc_svc ds ON ds.device_id=d.id
        JOIN services s ON s.id=ds.service_id
        WHERE ds.service_id=@service_id AND ds.deactivated_at IS NULL
		ORDER BY d.created_at DESC`
	args := pgx.NamedArgs{
		"service_id": svcID,
	}
	return QueryList[models.DeviceRecord](ctx, r.DB, query, args)
}

func (r *DeviceRepository) CreateTx(tx pgx.Tx, ctx context.Context, dvcID *appioid.ID, dvcReq models.DeviceCreateRequest) error {
	return r.createWithExecutor(tx, ctx, dvcID, dvcReq)
}

// Never updates `platform` and deactivated device
func (r *DeviceRepository) Update(ctx context.Context, dvcID *appioid.ID, dvcUpdateReq models.DeviceUpdateRequest) (bool, error) {
	return r.updateWithExecutor(r.DB, ctx, dvcID, dvcUpdateReq)
}

// Never updates `platform` and deactivated device
func (r *DeviceRepository) UpdateTx(tx pgx.Tx, ctx context.Context, dvcID *appioid.ID, dvcUpdateReq models.DeviceUpdateRequest) (bool, error) {
	return r.updateWithExecutor(tx, ctx, dvcID, dvcUpdateReq)
}

// --------------------------------------------------------------------------------------------------------------------

func (r *DeviceRepository) createWithExecutor(executor SQLExecutor, ctx context.Context, dvcID *appioid.ID, dvcReq models.DeviceCreateRequest) error {
	query := `INSERT INTO devices (
					id,
					name,
					platform,
					os_version,
					model,
					device_token,
                    device_identifier,
					notifications_enabled,
					marketing_name
				 ) VALUES (
					@id,
					@name,
					@platform,
					@os_version,
					@model,
					@device_token,
			        @device_identifier,
					@notifications_enabled,
					@marketing_name
				)`
	args := pgx.NamedArgs{
		"id":                    dvcID,
		"name":                  dvcReq.Name,
		"platform":              dvcReq.Platform,
		"os_version":            dvcReq.OsVersion,
		"model":                 dvcReq.Model,
		"device_token":          dvcReq.DeviceToken,
		"device_identifier":     dvcReq.DeviceIdentifier,
		"notifications_enabled": dvcReq.NotificationsEnabled,
		"marketing_name":        dvcReq.MarketingName,
	}

	_, err := executor.Exec(ctx, query, args)
	if err != nil {
		return fmt.Errorf("creating device: %w", err)
	}
	return nil
}

func (r *DeviceRepository) updateWithExecutor(executor SQLExecutor, ctx context.Context, dvcID *appioid.ID, dvcUpdateReq models.DeviceUpdateRequest) (bool, error) {
	query := "UPDATE devices SET"
	args := pgx.NamedArgs{
		"device_id": dvcID,
	}
	var updateFields []string

	if dvcUpdateReq.Name != "" { // can't be empty
		updateFields = append(updateFields, "name=@name")
		args["name"] = dvcUpdateReq.Name
	}
	if dvcUpdateReq.OsVersion != "" { // can't be empty
		updateFields = append(updateFields, "os_version=@os_version")
		args["os_version"] = dvcUpdateReq.OsVersion
	}
	if dvcUpdateReq.Model != "" { // can't be empty
		updateFields = append(updateFields, "model=@model")
		args["model"] = dvcUpdateReq.Model
	}
	if dvcUpdateReq.DeviceToken != "" { // can't be empty
		updateFields = append(updateFields, "device_token=@device_token")
		args["device_token"] = dvcUpdateReq.DeviceToken
	}
	if dvcUpdateReq.DeviceIdentifier != "" { // can't be empty
		updateFields = append(updateFields, "device_identifier=@device_identifier")
		args["device_identifier"] = dvcUpdateReq.DeviceIdentifier
	}
	if dvcUpdateReq.MarketingName != "" { // can be empty, but if provided, update it
		updateFields = append(updateFields, "marketing_name=@marketing_name")
		args["marketing_name"] = dvcUpdateReq.MarketingName
	}

	// NOTE: always update notifications_enabled
	updateFields = append(updateFields, "notifications_enabled=@notifications_enabled")
	args["notifications_enabled"] = dvcUpdateReq.NotificationsEnabled

	if len(updateFields) == 0 {
		return false, fmt.Errorf("no valid fields to update")
	}
	query += " " + strings.Join(updateFields, ", ") + " WHERE devices.id=@device_id"

	cmdTag, err := executor.Exec(ctx, query, args)
	if err != nil {
		return false, fmt.Errorf("updating device: %w", err)
	}
	return cmdTag.RowsAffected() > 0, nil
}

func (r *DeviceRepository) UpdateLastSeenAt(ctx context.Context, dvcID *appioid.ID) error {
	query := "UPDATE devices SET last_seen_at = now() WHERE id = @device_id"
	args := pgx.NamedArgs{
		"device_id": dvcID,
	}

	_, err := r.DB.Exec(ctx, query, args)
	if err != nil {
		return fmt.Errorf("updating device last_seen_at: %w", err)
	}
	return nil
}
