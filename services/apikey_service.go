package services

import (
	"api.appio.so/pkg/roles"
	"context"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"api.appio.so/pkg"
	"api.appio.so/pkg/config"
	"api.appio.so/repositories"
	"github.com/appio-so/go-appioid"
	"github.com/jackc/pgx/v5"
	"go.uber.org/zap"
)

type APIKeyService struct {
	repository repositories.APIKeyRepositoryInterface
	authKeys   *config.AuthConfig
	logger     *zap.Logger
	attempts   map[string]*APIKeyAttempt
	attemptsMu sync.RWMutex
	config     APIKeyServiceConfig
}

// APIKeyServiceConfig holds configurable parameters for the API key service
type APIKeyServiceConfig struct {
	MaxFailures      int           // Number of failures before blocking
	MaxBlockDuration time.Duration // Maximum block duration to prevent overflow
	MinKeyLength     int           // Minimum API key length
	MaxKeyLength     int           // Maximum API key length
	CleanupInterval  time.Duration // How often to clean up old attempts
	AttemptRetention time.Duration // How long to keep attempt records
}

type APIKeyAttempt struct {
	Attempts  int
	LastTry   time.Time
	BlockedAt *time.Time
}

// DefaultAPIKeyServiceConfig returns sensible defaults for the API key service
func DefaultAPIKeyServiceConfig() APIKeyServiceConfig {
	return APIKeyServiceConfig{
		MaxFailures:      5,
		MaxBlockDuration: 24 * time.Hour, // Cap at 24 hours to prevent overflow
		MinKeyLength:     50,
		MaxKeyLength:     200, // Allow longer keys for demo API keys
		CleanupInterval:  10 * time.Minute,
		AttemptRetention: 24 * time.Hour,
	}
}

func NewAPIKeyService(repository repositories.APIKeyRepositoryInterface, authKeys *config.AuthConfig, logger *zap.Logger) *APIKeyService {
	return NewAPIKeyServiceWithConfig(repository, authKeys, logger, DefaultAPIKeyServiceConfig())
}

func NewAPIKeyServiceWithConfig(repository repositories.APIKeyRepositoryInterface, authKeys *config.AuthConfig, logger *zap.Logger, config APIKeyServiceConfig) *APIKeyService {
	service := &APIKeyService{
		repository: repository,
		authKeys:   authKeys,
		logger:     logger,
		attempts:   make(map[string]*APIKeyAttempt),
		config:     config,
	}

	go func() {
		ticker := time.NewTicker(config.CleanupInterval)
		defer ticker.Stop()

		for range ticker.C {
			service.cleanupOldAttempts()
		}
	}()

	return service
}

// Quick and light validation
func (s *APIKeyService) IsValidApiKey(apiKey string) bool {
	return len(apiKey) >= s.config.MinKeyLength && len(apiKey) <= s.config.MaxKeyLength
}

func (s *APIKeyService) GetServiceIDAndRoleByAPIKey(ctx context.Context, apiKey string) (*appioid.ID, roles.Role, error) {
	// Validate API key length first
	if !s.IsValidApiKey(apiKey) {
		return nil, roles.Unknown, pkg.ErrInvalidInput
	}

	if s.isBlocked(apiKey) {
		return nil, roles.Unknown, pkg.ErrTooManyAttempts
	}

	svcID, role, err := s.getServiceIDAndRoleByAPIKey(ctx, apiKey)
	s.recordAttempt(apiKey, err == nil)
	return svcID, role, err
}

// --------------------------------------------------------------------------------------------------------------------

func (s *APIKeyService) getServiceIDAndRoleByAPIKey(ctx context.Context, apiKey string) (*appioid.ID, roles.Role, error) {
	// internal: app.appio.so
	if ok, appRole := s.isAppKey(apiKey); ok {
		return nil, appRole, nil
	}

	// internal: ios app
	if ok, iosRole := s.isIOSKey(apiKey); ok {
		return nil, iosRole, nil
	}

	// internal: android app
	if ok, iosRole := s.isAndroidKey(apiKey); ok {
		return nil, iosRole, nil
	}

	// internal: demo.appio.so
	if ok, demoRole := s.isDemoKey(apiKey); ok {
		return nil, demoRole, nil
	}

	// public: demo.appio.so curl API
	if ok, demoSvcID, demoRole, err := s.isDemoAPI(apiKey); ok {
		return demoSvcID, demoRole, err
	}

	// public: general use
	svcID, err := s.repository.GetServiceIDByAPIKey(ctx, apiKey)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			// This is expected
			return nil, roles.Unknown, nil
		}
		s.logger.Info("getting service ID and role", zap.Error(err))
		return nil, roles.Unknown, pkg.ErrInternal
	}

	// This should never be nil, but just in case
	if svcID == nil {
		return nil, roles.Unknown, nil
	}

	return svcID, roles.Api, nil
}

// Special handling for demo keys. ServiceID is the last 31 (`_demo` + 26 for ID) characters of the key
func (s *APIKeyService) isDemoAPI(apiKey string) (bool, *appioid.ID, roles.Role, error) {
	if !strings.HasPrefix(apiKey, "demo_") || len(apiKey) < 31 {
		return false, nil, "", nil
	}

	svcID, err := appioid.Parse(fmt.Sprintf("demo_svc_%s", apiKey[len(apiKey)-26:]))
	if err != nil {
		s.logger.Warn("parsing demo key", zap.String("key", apiKey), zap.Error(err))
		return true, nil, "", pkg.ErrInvalidInput
	}

	return true, svcID, roles.ApiDemo, nil
}

func (s *APIKeyService) isAppKey(apiKey string) (bool, roles.Role) {
	if apiKey == s.authKeys.App {
		return true, roles.AppAppioSo
	}
	return false, ""
}

func (s *APIKeyService) isIOSKey(apiKey string) (bool, roles.Role) {
	if apiKey == s.authKeys.IOS {
		return true, roles.IOS
	}
	return false, ""
}

func (s *APIKeyService) isAndroidKey(apiKey string) (bool, roles.Role) {
	if apiKey == s.authKeys.Android {
		return true, roles.Android
	}
	return false, ""
}

func (s *APIKeyService) isDemoKey(apiKey string) (bool, roles.Role) {
	if apiKey == s.authKeys.Demo {
		return true, roles.DemoAppioSo
	}
	return false, ""
}

func (s *APIKeyService) isBlocked(apiKey string) bool {
	s.attemptsMu.RLock()
	attempt, exists := s.attempts[apiKey]
	if !exists {
		s.attemptsMu.RUnlock()
		return false
	}

	blocked := false
	if attempt.BlockedAt != nil {
		// Simple fixed block duration - much simpler and more predictable
		blocked = time.Since(*attempt.BlockedAt) < s.config.MaxBlockDuration
	}
	s.attemptsMu.RUnlock()

	return blocked
}

func (s *APIKeyService) recordAttempt(apiKey string, success bool) {
	s.attemptsMu.Lock()
	defer s.attemptsMu.Unlock()

	attempt, exists := s.attempts[apiKey]
	if !exists {
		attempt = &APIKeyAttempt{}
		s.attempts[apiKey] = attempt
	}

	if success {
		delete(s.attempts, apiKey) // Reset on success
		return
	}

	attempt.Attempts++
	attempt.LastTry = time.Now()

	if attempt.Attempts >= s.config.MaxFailures { // Block after configured failures
		now := time.Now()
		attempt.BlockedAt = &now
	}
}

func (s *APIKeyService) cleanupOldAttempts() {
	s.attemptsMu.Lock()
	defer s.attemptsMu.Unlock()

	now := time.Now()
	for key, attempt := range s.attempts {
		// Remove entries older than configured retention period
		if now.Sub(attempt.LastTry) > s.config.AttemptRetention {
			delete(s.attempts, key)
		}
	}
}
