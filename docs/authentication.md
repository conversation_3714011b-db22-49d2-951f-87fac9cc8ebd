# Authentication

We have multiple sets of routs authenticated by Auth token in the header.
Role and ServiceID are retrieved from DB by on Auth token.
If the Auth token starts with `demo_`, ServiceID is extracted from the Auth token. ServiceID is composed as `demo_svc_`
followed by the last 26 characters of Auth token.

- `/mobile/*` is used by iOS and Android mobile apps. It uses: Roles.IOS, Roles.Android
    No authentication is used but Auth token is still expected to satisfy the middleware. Its value is ignored.
    These routes are wide open to anyone.
    ServiceID in `X-Service-Id` header is mostly required. Except for registering and updating device, listing services associated with DeviceID.
    DeviceID in `X-Device-Id` header is used for some routes.
- `/demo-appio-so/*` is used by demo.appio.so app. It uses Roles.DemoAppioSo
    These routes are private.
    ServiceID in `X-Service-Id` header is required. Except of creating a new service.
    Only ServiceIDs prefixed with `demo_svc` are allowed.
- `/app-appio-so/*` is used by app.appio.so app. It uses Roles.AppAppioSo
    These routes are private.
    Only `/services` route requires ServiceID in `X-Service-Id` header and URL.
- `/v1/*` is the main API. It uses Roles.ApiUser.
    Header `X-Service-Id` is not used.