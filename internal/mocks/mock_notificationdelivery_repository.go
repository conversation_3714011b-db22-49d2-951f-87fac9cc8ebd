// Code generated by MockGen. DO NOT EDIT.
// Source: repositories/notificationdelivery_repository.go
//
// Generated by this command:
//
//	mockgen -source=repositories/notificationdelivery_repository.go -destination=internal/mocks/mock_notificationdelivery_repository.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	models "api.appio.so/models"
	notification_status "api.appio.so/models/notification_status"
	appioid "github.com/appio-so/go-appioid"
	pgx "github.com/jackc/pgx/v5"
	gomock "go.uber.org/mock/gomock"
)

// MockNotificationDeliveryRepositoryInterface is a mock of NotificationDeliveryRepositoryInterface interface.
type MockNotificationDeliveryRepositoryInterface struct {
	ctrl     *gomock.Controller
	recorder *MockNotificationDeliveryRepositoryInterfaceMockRecorder
	isgomock struct{}
}

// MockNotificationDeliveryRepositoryInterfaceMockRecorder is the mock recorder for MockNotificationDeliveryRepositoryInterface.
type MockNotificationDeliveryRepositoryInterfaceMockRecorder struct {
	mock *MockNotificationDeliveryRepositoryInterface
}

// NewMockNotificationDeliveryRepositoryInterface creates a new mock instance.
func NewMockNotificationDeliveryRepositoryInterface(ctrl *gomock.Controller) *MockNotificationDeliveryRepositoryInterface {
	mock := &MockNotificationDeliveryRepositoryInterface{ctrl: ctrl}
	mock.recorder = &MockNotificationDeliveryRepositoryInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNotificationDeliveryRepositoryInterface) EXPECT() *MockNotificationDeliveryRepositoryInterfaceMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockNotificationDeliveryRepositoryInterface) Create(ctx context.Context, ntfdlvID, notificationID, dvcID *appioid.ID, status notification_status.Status) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, ntfdlvID, notificationID, dvcID, status)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockNotificationDeliveryRepositoryInterfaceMockRecorder) Create(ctx, ntfdlvID, notificationID, dvcID, status any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockNotificationDeliveryRepositoryInterface)(nil).Create), ctx, ntfdlvID, notificationID, dvcID, status)
}

// CreateTx mocks base method.
func (m *MockNotificationDeliveryRepositoryInterface) CreateTx(tx pgx.Tx, ctx context.Context, ntfdlvID, notificationID, dvcID *appioid.ID, status notification_status.Status) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTx", tx, ctx, ntfdlvID, notificationID, dvcID, status)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateTx indicates an expected call of CreateTx.
func (mr *MockNotificationDeliveryRepositoryInterfaceMockRecorder) CreateTx(tx, ctx, ntfdlvID, notificationID, dvcID, status any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTx", reflect.TypeOf((*MockNotificationDeliveryRepositoryInterface)(nil).CreateTx), tx, ctx, ntfdlvID, notificationID, dvcID, status)
}

// ListByCustomerUserID mocks base method.
func (m *MockNotificationDeliveryRepositoryInterface) ListByCustomerUserID(ctx context.Context, svcID *appioid.ID, customerUserID string, statuses []notification_status.Status) ([]models.NotificationDeliveryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByCustomerUserID", ctx, svcID, customerUserID, statuses)
	ret0, _ := ret[0].([]models.NotificationDeliveryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByCustomerUserID indicates an expected call of ListByCustomerUserID.
func (mr *MockNotificationDeliveryRepositoryInterfaceMockRecorder) ListByCustomerUserID(ctx, svcID, customerUserID, statuses any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByCustomerUserID", reflect.TypeOf((*MockNotificationDeliveryRepositoryInterface)(nil).ListByCustomerUserID), ctx, svcID, customerUserID, statuses)
}

// ListByDevice mocks base method.
func (m *MockNotificationDeliveryRepositoryInterface) ListByDevice(ctx context.Context, svcID, dvcID *appioid.ID, statuses []notification_status.Status) ([]models.NotificationDeliveryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByDevice", ctx, svcID, dvcID, statuses)
	ret0, _ := ret[0].([]models.NotificationDeliveryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByDevice indicates an expected call of ListByDevice.
func (mr *MockNotificationDeliveryRepositoryInterfaceMockRecorder) ListByDevice(ctx, svcID, dvcID, statuses any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByDevice", reflect.TypeOf((*MockNotificationDeliveryRepositoryInterface)(nil).ListByDevice), ctx, svcID, dvcID, statuses)
}

// Update mocks base method.
func (m *MockNotificationDeliveryRepositoryInterface) Update(ctx context.Context, svcID, ntfID, dvcID *appioid.ID, status notification_status.Status) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, svcID, ntfID, dvcID, status)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockNotificationDeliveryRepositoryInterfaceMockRecorder) Update(ctx, svcID, ntfID, dvcID, status any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockNotificationDeliveryRepositoryInterface)(nil).Update), ctx, svcID, ntfID, dvcID, status)
}
