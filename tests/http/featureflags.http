# Setup
@host = http://localhost:8082

###
# @name LIST FEATURE FLAGS as ios
GET {{host}}/mobile/ff
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222
X-App-Version: 1.1
X-App-Platform: ios

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name LIST FEATURE FLAGS as android
GET {{host}}/mobile/ff
Authorization: Bearer dev_44444444444444444444444444444444444444444444444444
X-App-Version: 1.0
X-App-Platform: android

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}