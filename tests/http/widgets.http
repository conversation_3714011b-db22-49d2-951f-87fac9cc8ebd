# Setup
@host = http://localhost:8082

###
# @name LIST WIDGETS
GET {{host}}/v1/widgets
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name GET WIDGET
GET {{host}}/v1/widgets/wgt_00000000000000000000000000
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name GET WIDGET - not found
GET {{host}}/v1/widgets/wgt_00000000xxxxxxxxxxxxxxxxxx
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

> {%
    client.test("Response status should be 404", () => {
        client.assert(response.status === 404, `Response status was: ${response.status}`)
    })
%}

###
# @name GET WIDGETS as demo.appio.so - not found
GET {{host}}/demo-appio-so/widgets/wgt_00000000000000000000000000
Authorization: Bearer dev_11111111111111111111111111111111111111111111111111
X-Service-Id: demo_svc_00000000000000000000000000

> {%
    client.test("Response status should be 404", () => {
        client.assert(response.status === 404, `Response status was: ${response.status}`)
    })
%}

###
# @name GET WIDGET as ios
GET {{host}}/mobile/widgets/wgt_00000000000000000000000000
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222
X-Service-Id: svc_00000000000000000000000000
#X-Device-Id: dvc_00000000000000000000000000   # not needed, but client might send it if it has it

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name CREATE WIDGET
POST {{host}}/v1/widgets
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

{
  "template": "bar chart",
  "source": {
    "data": 123,
    "type": "static"
  }
}

> {%
    client.global.set("widget_id", response.body.id);
    client.test("Response status should be 201", () => {
        client.assert(response.status === 201, `Response status was: ${response.status}`)
    })
%}

###
# @name CREATE WIDGET as demo.appio.so
POST {{host}}/demo-appio-so/widgets
Authorization: Bearer dev_11111111111111111111111111111111111111111111111111
X-Service-Id: demo_svc_00000000000000000000000000

{
  "template": "bar chart"
}

> {%
    client.global.set("demo_widget_id", response.body.id);
    client.test("Response status should be 201", () => {
        client.assert(response.status === 201, `Response status was: ${response.status}`)
    })
%}

###
# @name UPDATE WIDGET
PATCH {{host}}/v1/widgets/{{widget_id}}
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

{
  "template": "line chart"
}

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name UPDATE WIDGET as demo.appio.so
PATCH {{host}}/demo-appio-so/widgets/{{demo_widget_id}}
Authorization: Bearer dev_11111111111111111111111111111111111111111111111111
X-Service-Id: demo_svc_00000000000000000000000000

{
  "template": "demo chart"
}

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name UPDATE WIDGET as demo.appio.so - not found
PATCH {{host}}/demo-appio-so/widgets/{{widget_id}}
Authorization: Bearer dev_11111111111111111111111111111111111111111111111111
X-Service-Id: demo_svc_00000000000000000000000000

{
  "template": "demo chart"
}

> {%
    client.test("Response status should be 404", () => {
        client.assert(response.status === 404, `Response status was: ${response.status}`)
    })
%}

###
# @name DELETE WIDGET
DELETE {{host}}/v1/widgets/{{widget_id}}
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name DELETE WIDGET - retry fails
DELETE {{host}}/v1/widgets/{{widget_id}}
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

> {%
    client.test("Response status should be 404", () => {
        client.assert(response.status === 404, `Response status was: ${response.status}`)
    })
%}

###
# @name UPDATE WIDGET - deleted fails
PATCH {{host}}/v1/widgets/{{widget_id}}
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

{
  "template": "line chart"
}

> {%
    client.test("Response status should be 404", () => {
        client.assert(response.status === 404, `Response status was: ${response.status}`)
    })
%}