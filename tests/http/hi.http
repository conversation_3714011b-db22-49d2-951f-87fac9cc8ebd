# Setup
@host = http://localhost:8082

###
# @name HI
GET {{host}}/hi
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name HI Android
GET {{host}}/hi
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name HI ios
GET {{host}}/hi
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name HI app.appio.so
GET {{host}}/hi
Authorization: Bearer dev_33333333333333333333333333333333333333333333333333

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}

###
# @name HI demo.appio.so
GET {{host}}/hi
Authorization: Bearer dev_11111111111111111111111111111111111111111111111111

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })
%}
