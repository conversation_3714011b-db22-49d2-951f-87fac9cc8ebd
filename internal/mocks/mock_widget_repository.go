// Code generated by MockGen. DO NOT EDIT.
// Source: repositories/widget_repository.go
//
// Generated by this command:
//
//	mockgen -source=repositories/widget_repository.go -destination=internal/mocks/mock_widget_repository.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	models "api.appio.so/models"
	appioid "github.com/appio-so/go-appioid"
	gomock "go.uber.org/mock/gomock"
)

// MockWidgetRepositoryInterface is a mock of WidgetRepositoryInterface interface.
type MockWidgetRepositoryInterface struct {
	ctrl     *gomock.Controller
	recorder *MockWidgetRepositoryInterfaceMockRecorder
	isgomock struct{}
}

// MockWidgetRepositoryInterfaceMockRecorder is the mock recorder for MockWidgetRepositoryInterface.
type MockWidgetRepositoryInterfaceMockRecorder struct {
	mock *MockWidgetRepositoryInterface
}

// NewMockWidgetRepositoryInterface creates a new mock instance.
func NewMockWidgetRepositoryInterface(ctrl *gomock.Controller) *MockWidgetRepositoryInterface {
	mock := &MockWidgetRepositoryInterface{ctrl: ctrl}
	mock.recorder = &MockWidgetRepositoryInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWidgetRepositoryInterface) EXPECT() *MockWidgetRepositoryInterfaceMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockWidgetRepositoryInterface) Create(ctx context.Context, svcID, wgtID *appioid.ID, wgtReq models.WidgetRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, svcID, wgtID, wgtReq)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockWidgetRepositoryInterfaceMockRecorder) Create(ctx, svcID, wgtID, wgtReq any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockWidgetRepositoryInterface)(nil).Create), ctx, svcID, wgtID, wgtReq)
}

// Delete mocks base method.
func (m *MockWidgetRepositoryInterface) Delete(ctx context.Context, svcID, wgtID *appioid.ID) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, svcID, wgtID)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Delete indicates an expected call of Delete.
func (mr *MockWidgetRepositoryInterfaceMockRecorder) Delete(ctx, svcID, wgtID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockWidgetRepositoryInterface)(nil).Delete), ctx, svcID, wgtID)
}

// FindByID mocks base method.
func (m *MockWidgetRepositoryInterface) FindByID(ctx context.Context, svcID, wgtID *appioid.ID) (*models.Widget, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByID", ctx, svcID, wgtID)
	ret0, _ := ret[0].(*models.Widget)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByID indicates an expected call of FindByID.
func (mr *MockWidgetRepositoryInterfaceMockRecorder) FindByID(ctx, svcID, wgtID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByID", reflect.TypeOf((*MockWidgetRepositoryInterface)(nil).FindByID), ctx, svcID, wgtID)
}

// List mocks base method.
func (m *MockWidgetRepositoryInterface) List(ctx context.Context, svcID *appioid.ID) ([]models.Widget, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, svcID)
	ret0, _ := ret[0].([]models.Widget)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockWidgetRepositoryInterfaceMockRecorder) List(ctx, svcID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockWidgetRepositoryInterface)(nil).List), ctx, svcID)
}

// Update mocks base method.
func (m *MockWidgetRepositoryInterface) Update(ctx context.Context, svcID, wgtID *appioid.ID, wgtReq models.WidgetRequest) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, svcID, wgtID, wgtReq)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockWidgetRepositoryInterfaceMockRecorder) Update(ctx, svcID, wgtID, wgtReq any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockWidgetRepositoryInterface)(nil).Update), ctx, svcID, wgtID, wgtReq)
}
