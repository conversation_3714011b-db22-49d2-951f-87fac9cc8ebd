package middlewares

import (
	"api.appio.so/models"
	"api.appio.so/pkg/roles"
	"context"
	"github.com/appio-so/go-appioid"
)

// Define context keys
type PlatformKey struct{}
type Svc<PERSON><PERSON><PERSON> struct{}
type Dvc<PERSON><PERSON>ey struct{}
type roleKey struct{}

func GetPlatformFromContext(ctx context.Context) (models.Platform, bool) {
	platform, ok := ctx.Value(PlatformKey{}).(models.Platform)
	if !ok {
		return "", false
	}
	return platform, ok
}

func GetServiceIDFromContext(ctx context.Context) (*appioid.ID, bool) {
	svcID, ok := ctx.Value(SvcIDKey{}).(*appioid.ID)
	if !ok {
		return nil, false
	}
	return svcID, ok
}

func GetDeviceIDFromContext(ctx context.Context) (*appioid.ID, bool) {
	dvcID, ok := ctx.Value(DvcIDKey{}).(*appioid.ID)
	if !ok {
		return nil, false
	}
	return dvcID, ok
}

func GetRoleFromContext(ctx context.Context) (roles.Role, bool) {
	role, ok := ctx.Value(roleKey{}).(roles.Role)
	return role, ok
}
