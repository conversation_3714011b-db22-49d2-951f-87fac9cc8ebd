package services

import (
	"api.appio.so/models"
	"api.appio.so/models/notification_status"
	"api.appio.so/models/notification_type"
	"api.appio.so/pkg"
	"api.appio.so/pkg/database"
	"api.appio.so/repositories"
	"context"
	"errors"
	"github.com/appio-so/go-appioid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

// NotificationServiceInterface defines the interface for notification service operations
type NotificationServiceInterface interface {
	FindByID(ctx context.Context, svcID, ntfID *appioid.ID) (*models.NotificationResponseWithStats, error)
	List(ctx context.Context, svcID *appioid.ID, status string) ([]models.NotificationResponse, error)
	ListAllByDevice(ctx context.Context, svcID, dvcID *appioid.ID, status string) ([]models.NotificationDeliveryResponse, error)
	ListAllByCustomerUserID(ctx context.Context, svcID *appioid.ID, customerUserID, status string) ([]models.NotificationDeliveryResponse, error)
	ListDeliveredByDevice(ctx context.Context, svcID, dvcID *appioid.ID) ([]models.NotificationDeliveryResponse, error)
	CreateForeground(ctx context.Context, svcID, dvcID *appioid.ID, customerUserID string, ntfReq models.NotificationRequest) (*appioid.ID, error)
}

type NotificationService struct {
	notificationRepository repositories.NotificationRepositoryInterface
	ntfdlvRepository       repositories.NotificationDeliveryRepositoryInterface
	deviceRepository       repositories.DeviceRepositoryInterface
	DB                     *pgxpool.Pool
	logger                 *zap.Logger
	TxExecutor             database.TxExecutorFunc
}

func NewNotificationService(notificationRepository repositories.NotificationRepositoryInterface, ntfdlvRepository repositories.NotificationDeliveryRepositoryInterface, deviceRepository repositories.DeviceRepositoryInterface, db *pgxpool.Pool, logger *zap.Logger) *NotificationService {
	return &NotificationService{
		notificationRepository: notificationRepository,
		ntfdlvRepository:       ntfdlvRepository,
		deviceRepository:       deviceRepository,
		DB:                     db,
		logger:                 logger,
		TxExecutor:             database.ExecuteTx,
	}
}

func (s *NotificationService) FindByID(ctx context.Context, svcID, ntfID *appioid.ID) (*models.NotificationResponseWithStats, error) {
	notification, err := s.notificationRepository.FindByID(ctx, svcID, ntfID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, pkg.ErrNotFound
		}

		s.logger.Error("finding notification", zap.Error(err))
		return nil, pkg.ErrInternal
	}
	return notification, nil
}

func (s *NotificationService) ListDeliveredByDevice(ctx context.Context, svcID, dvcID *appioid.ID) ([]models.NotificationDeliveryResponse, error) {
	notifications, err := s.ntfdlvRepository.ListByDevice(ctx, svcID, dvcID, []notification_status.Status{
		notification_status.Completed,
	})
	if err != nil {
		s.logger.Error("listing delivered notifications", zap.Error(err))
		return nil, pkg.ErrInternal
	}
	return notifications, nil
}

func (s *NotificationService) List(ctx context.Context, svcID *appioid.ID, status string) ([]models.NotificationResponse, error) {
	notifications, err := s.notificationRepository.List(ctx, svcID, s.statusFilter(status))
	if err != nil {
		s.logger.Error("listing notifications", zap.Error(err))
		return nil, pkg.ErrInternal
	}
	return notifications, nil
}

func (s *NotificationService) ListWithStats(ctx context.Context, svcID *appioid.ID, status string) ([]models.NotificationResponseWithStats, error) {
	notifications, err := s.notificationRepository.ListWithStats(ctx, svcID, s.statusFilter(status))
	if err != nil {
		s.logger.Error("listing notifications", zap.Error(err))
		return nil, pkg.ErrInternal
	}
	return notifications, nil
}

func (s *NotificationService) ListAllByDevice(ctx context.Context, svcID, dvcID *appioid.ID, status string) ([]models.NotificationDeliveryResponse, error) {
	notifications, err := s.ntfdlvRepository.ListByDevice(ctx, svcID, dvcID, s.statusFilter(status))
	if err != nil {
		s.logger.Error("listing notifications by device", zap.Error(err))
		return nil, pkg.ErrInternal
	}
	return notifications, nil
}

func (s *NotificationService) ListAllByCustomerUserID(ctx context.Context, svcID *appioid.ID, customerUserID, status string) ([]models.NotificationDeliveryResponse, error) {
	notifications, err := s.ntfdlvRepository.ListByCustomerUserID(ctx, svcID, customerUserID, s.statusFilter(status))
	if err != nil {
		s.logger.Error("listing notifications by customerUserID", zap.Error(err))
		return nil, pkg.ErrInternal
	}
	return notifications, nil
}

func (s *NotificationService) CreateForeground(ctx context.Context, svcID, dvcID *appioid.ID, customerUserID string, ntfReq models.NotificationRequest) (ntfID *appioid.ID, err error) {
	ntfID, err = appioid.New(models.NotificationPrefix)
	if err != nil {
		return nil, pkg.ErrInternal
	}

	if dvcID != nil {
		return ntfID, s.createNotificationForDevice(ctx, ntfID, svcID, dvcID, ntfReq)
	}

	if customerUserID != "" {
		return ntfID, s.createNotificationForCustomerUserID(ctx, ntfID, svcID, customerUserID, ntfReq)
	}

	return ntfID, s.createNotificationForAllDevices(ctx, ntfID, svcID, ntfReq)
}

// --------------------------------------------------------------------------------------------------------------------

func (s *NotificationService) createNotificationForAllDevices(ctx context.Context, ntfID, svcID *appioid.ID, ntfReq models.NotificationRequest) error {
	if err := s.notificationRepository.Create(ctx, notification_type.Foreground, ntfID, svcID, ntfReq, notification_status.Created); err != nil {
		s.logger.Error("creating notification", zap.String("ntfID", ntfID.String()), zap.String("svcID", svcID.String()), zap.Error(err))
		return pkg.ErrInternal
	}

	return nil
}

func (s *NotificationService) createNotificationForDevice(ctx context.Context, ntfID, svcID, dvcID *appioid.ID, ntfReq models.NotificationRequest) error {
	return s.TxExecutor(ctx, s.DB, s.logger, func(tx pgx.Tx) error {
		device, err := s.deviceRepository.FindByID(ctx, svcID, dvcID)
		if err != nil {
			if errors.Is(err, pgx.ErrNoRows) {
				return pkg.ErrNotFound
			}
			s.logger.Error("finding widget", zap.Error(err))
			return pkg.ErrInternal
		}

		if err := s.notificationRepository.CreateTx(tx, ctx, notification_type.Foreground, ntfID, svcID, ntfReq, notification_status.Queued); err != nil {
			s.logger.Error("creating notification for device", zap.String("ntfID", ntfID.String()), zap.String("dvcID", dvcID.String()), zap.String("svcID", svcID.String()), zap.Error(err))
			return pkg.ErrInternal
		}

		ntfDlvID, err := appioid.New(models.NotificationDeliveryPrefix)
		if err != nil {
			return pkg.ErrInternal
		}

		if err := s.ntfdlvRepository.CreateTx(tx, ctx, ntfDlvID, ntfID, device.ID, notification_status.Queued); err != nil {
			s.logger.Error("creating notification delivery for device", zap.String("ntfID", ntfID.String()), zap.String("dvcID", device.ID.String()), zap.String("svcID", svcID.String()), zap.Error(err))
			return pkg.ErrInternal
		}

		return nil
	})
}

func (s *NotificationService) createNotificationForCustomerUserID(ctx context.Context, ntfID, svcID *appioid.ID, customerUserID string, ntfReq models.NotificationRequest) error {
	return s.TxExecutor(ctx, s.DB, s.logger, func(tx pgx.Tx) error {
		devices, err := s.deviceRepository.FindByCustomerUserID(ctx, svcID, customerUserID)
		if err != nil {
			s.logger.Error("finding devices by customerUserID", zap.String("customerUserID", customerUserID), zap.String("svcID", svcID.String()), zap.Error(err))
			return pkg.ErrInternal
		}
		if len(devices) == 0 {
			return pkg.ErrNotFound
		}

		if err := s.notificationRepository.CreateTx(tx, ctx, notification_type.Foreground, ntfID, svcID, ntfReq, notification_status.Queued); err != nil {
			s.logger.Error("creating notification for customerUserID", zap.String("ntfID", ntfID.String()), zap.String("customerUserID", customerUserID), zap.String("svcID", svcID.String()), zap.Error(err))
			return pkg.ErrInternal
		}

		for _, device := range devices {
			ntfDlvID, err := appioid.New(models.NotificationDeliveryPrefix)
			if err != nil {
				return pkg.ErrInternal
			}

			if err := s.ntfdlvRepository.CreateTx(tx, ctx, ntfDlvID, ntfID, device.ID, notification_status.Queued); err != nil {
				s.logger.Error("creating notification delivery for customerUserID and device", zap.String("ntfID", ntfID.String()), zap.String("dvcID", device.ID.String()), zap.String("customerUserID", customerUserID), zap.String("svcID", svcID.String()), zap.Error(err))
				return pkg.ErrInternal
			}
		}

		return nil
	})
}

func (s *NotificationService) statusFilter(status string) []notification_status.Status {
	if status != "" {
		if validStatus := notification_status.Status(status); validStatus.IsValidStatus() {
			return []notification_status.Status{validStatus}
		}
	}

	return notification_status.All()
}
