// Code generated by MockGen. DO NOT EDIT.
// Source: services/notification_service.go
//
// Generated by this command:
//
//	mockgen -source=services/notification_service.go -destination=internal/mocks/mock_notification_service.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	models "api.appio.so/models"
	appioid "github.com/appio-so/go-appioid"
	gomock "go.uber.org/mock/gomock"
)

// MockNotificationServiceInterface is a mock of NotificationServiceInterface interface.
type MockNotificationServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockNotificationServiceInterfaceMockRecorder
	isgomock struct{}
}

// MockNotificationServiceInterfaceMockRecorder is the mock recorder for MockNotificationServiceInterface.
type MockNotificationServiceInterfaceMockRecorder struct {
	mock *MockNotificationServiceInterface
}

// NewMockNotificationServiceInterface creates a new mock instance.
func NewMockNotificationServiceInterface(ctrl *gomock.Controller) *MockNotificationServiceInterface {
	mock := &MockNotificationServiceInterface{ctrl: ctrl}
	mock.recorder = &MockNotificationServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNotificationServiceInterface) EXPECT() *MockNotificationServiceInterfaceMockRecorder {
	return m.recorder
}

// CreateForeground mocks base method.
func (m *MockNotificationServiceInterface) CreateForeground(ctx context.Context, svcID, dvcID *appioid.ID, customerUserID string, ntfReq models.NotificationRequest) (*appioid.ID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateForeground", ctx, svcID, dvcID, customerUserID, ntfReq)
	ret0, _ := ret[0].(*appioid.ID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateForeground indicates an expected call of CreateForeground.
func (mr *MockNotificationServiceInterfaceMockRecorder) CreateForeground(ctx, svcID, dvcID, customerUserID, ntfReq any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateForeground", reflect.TypeOf((*MockNotificationServiceInterface)(nil).CreateForeground), ctx, svcID, dvcID, customerUserID, ntfReq)
}

// FindByID mocks base method.
func (m *MockNotificationServiceInterface) FindByID(ctx context.Context, svcID, ntfID *appioid.ID) (*models.NotificationResponseWithStats, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByID", ctx, svcID, ntfID)
	ret0, _ := ret[0].(*models.NotificationResponseWithStats)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByID indicates an expected call of FindByID.
func (mr *MockNotificationServiceInterfaceMockRecorder) FindByID(ctx, svcID, ntfID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByID", reflect.TypeOf((*MockNotificationServiceInterface)(nil).FindByID), ctx, svcID, ntfID)
}

// List mocks base method.
func (m *MockNotificationServiceInterface) List(ctx context.Context, svcID *appioid.ID, status string) ([]models.NotificationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, svcID, status)
	ret0, _ := ret[0].([]models.NotificationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockNotificationServiceInterfaceMockRecorder) List(ctx, svcID, status any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockNotificationServiceInterface)(nil).List), ctx, svcID, status)
}

// ListAllByCustomerUserID mocks base method.
func (m *MockNotificationServiceInterface) ListAllByCustomerUserID(ctx context.Context, svcID *appioid.ID, customerUserID, status string) ([]models.NotificationDeliveryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAllByCustomerUserID", ctx, svcID, customerUserID, status)
	ret0, _ := ret[0].([]models.NotificationDeliveryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAllByCustomerUserID indicates an expected call of ListAllByCustomerUserID.
func (mr *MockNotificationServiceInterfaceMockRecorder) ListAllByCustomerUserID(ctx, svcID, customerUserID, status any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAllByCustomerUserID", reflect.TypeOf((*MockNotificationServiceInterface)(nil).ListAllByCustomerUserID), ctx, svcID, customerUserID, status)
}

// ListAllByDevice mocks base method.
func (m *MockNotificationServiceInterface) ListAllByDevice(ctx context.Context, svcID, dvcID *appioid.ID, status string) ([]models.NotificationDeliveryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAllByDevice", ctx, svcID, dvcID, status)
	ret0, _ := ret[0].([]models.NotificationDeliveryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAllByDevice indicates an expected call of ListAllByDevice.
func (mr *MockNotificationServiceInterfaceMockRecorder) ListAllByDevice(ctx, svcID, dvcID, status any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAllByDevice", reflect.TypeOf((*MockNotificationServiceInterface)(nil).ListAllByDevice), ctx, svcID, dvcID, status)
}

// ListDeliveredByDevice mocks base method.
func (m *MockNotificationServiceInterface) ListDeliveredByDevice(ctx context.Context, svcID, dvcID *appioid.ID) ([]models.NotificationDeliveryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListDeliveredByDevice", ctx, svcID, dvcID)
	ret0, _ := ret[0].([]models.NotificationDeliveryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListDeliveredByDevice indicates an expected call of ListDeliveredByDevice.
func (mr *MockNotificationServiceInterfaceMockRecorder) ListDeliveredByDevice(ctx, svcID, dvcID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListDeliveredByDevice", reflect.TypeOf((*MockNotificationServiceInterface)(nil).ListDeliveredByDevice), ctx, svcID, dvcID)
}
